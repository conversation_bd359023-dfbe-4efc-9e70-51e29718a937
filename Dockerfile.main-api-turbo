# Base image for all stages, providing Bun runtime
FROM oven/bun:1 AS base
WORKDIR /app

# --- Manifests Stage ---
# Copies all root and workspace package.json files, and the root bun.lock file.
# This stage creates a consistent context for dependency installation, optimized for caching.
FROM base AS manifests
WORKDIR /app
COPY package.json bun.lock ./
# Copy all workspace package.json files. Ensure this list is complete.
COPY apps/main-api/package.json ./apps/main-api/
COPY apps/dashboard/package.json ./apps/dashboard/
COPY apps/mini-app/package.json ./apps/mini-app/
COPY packages/db/package.json ./packages/db/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

# If you have more workspaces, add their package.json files here, e.g.:
# COPY packages/another-package/package.json ./packages/another-package/

# --- Monorepo Production Dependencies Installation Stage ---
# Installs ONLY PRODUCTION dependencies for the entire monorepo.
# This stage is used by the 'runner' stage to keep the final image lean.
FROM manifests AS monorepo-prod-deps-installer
WORKDIR /app
RUN bun install --production

# --- Pruner Stage ---
# Uses Turborepo to prune the monorepo, isolating only the source code
# required for the 'main-api' application and its local dependencies.
FROM base AS pruner
WORKDIR /app
# Install Turborepo CLI
RUN bun install -g turbo@latest
# Copy the full monorepo source code to allow Turborepo to analyze the dependency graph
COPY . .
# Prune the monorepo for the 'main-api' application.
# The --docker flag structures the output for Docker builds.
RUN turbo prune main-api --docker
# Pruned source code will be available in /app/out/full/

# --- Builder Stage ---
# Builds the 'main-api' application using the pruned source code and full dependencies.
FROM base AS builder
WORKDIR /app
ENV NODE_ENV=production

# Copy the pruned lockfile
COPY --from=pruner /app/out/bun.lock ./bun.lock

# Copy pruned package.json files. Turborepo's `prune --docker` command
# creates an `out/json` directory with just the package.json files,
# structured correctly.
COPY --from=pruner /app/out/json/ ./

# Copy pruned source code for 'main-api' and its local dependencies.
# The source structure (e.g., apps/main-api, packages/some-dep) is preserved from /app/out/full/
# This also includes the package.json files within their respective directories.
COPY --from=pruner /app/out/full/ .

# Install dependencies for the pruned application. This will create node_modules
# with correct symlinks for local packages like @monorepo/db and @repo/typescript-config.
# We install with devDependencies as the build process might require them.
# Running without --frozen-lockfile here because the bun.lock from `turbo prune`
# (which might be the original monorepo lockfile or a generated one)
# can sometimes have slight inconsistencies with the pruned package.json files.
# This allows bun to resolve dependencies purely based on the pruned package.json files.
# This will also install 'turbo' if it's a devDependency in the root package.json.
RUN bun install

# Use Turborepo to build the 'main-api' application and its dependencies.
# 'bunx' will execute turbo from the local node_modules/.bin or download it if not found.
# This command is run from the root of the pruned monorepo (/app).
RUN bunx turbo run build --filter=main-api
# Build artifacts are typically generated in a 'dist' folder within /app/apps/main-api/

# --- Final Runner Stage ---
# Creates the final, lightweight image for running the 'main-api' application in production.
FROM oven/bun:1-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

# Create a non-root user and group for security best practices
RUN addgroup -S nonroot && adduser -S nonroot -G nonroot

# Copy ONLY production node_modules for the entire monorepo
COPY --from=monorepo-prod-deps-installer /app/node_modules ./node_modules
# Copy the built application artifacts from the builder stage
COPY --from=builder /app/apps/main-api/dist ./dist
# Copy the application's specific package.json to /app/package.json.
# This is needed for 'bun run' to find scripts in the runner stage.
COPY --from=pruner /app/out/full/apps/main-api/package.json ./package.json
# Copy the bun.lock file that corresponds to the installed production dependencies
COPY --from=monorepo-prod-deps-installer /app/bun.lock ./bun.lock

# Switch to the non-root user
USER nonroot

EXPOSE 3004
# Define the command to run the application using the 'start:prod' script
# from /app/package.json (which is main-api's package.json)
CMD ["bun", "run", "start:prod"]
