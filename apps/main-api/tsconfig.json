{
	"extends": "@repo/typescript-config/base.json",
	"compilerOptions": {
		"outDir": "./dist",
		"rootDir": "src",
		"baseUrl": ".", // baseUrl is important for paths to work correctly
		"paths": {
			"@/*": ["src/*"], // For internal main-api aliases
			// THIS IS THE CRITICAL PART:
			"@monorepo/db": ["../../packages/db/dist/index.js"],
			"@monorepo/db/*": ["../../packages/db/dist/*"]
		},
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"declaration": true,
		"declarationMap": true,
		"removeComments": true,
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		"target": "ES2021",
		"module": "NodeNext",
		"moduleResolution": "NodeNext",
		"sourceMap": true,
		"noEmit": false, // Important for nest build which uses tsc/swc to emit
		"composite": true, // Good for project references
		"lib": ["ES2021"],
		"types": ["node"]
	},
	"references": [
		{ "path": "../../packages/db" } // This tells TS about the dependency project
	],
	"include": [
		"src/**/*" // Ensure this covers all your source files for main-api
	],
	"exclude": [
		"node_modules",
		"dist",
		"test",
		"**/*spec.ts"
		// DO NOT exclude ../../packages/db/dist here.
		// The compiler needs to read .d.ts files from there.
	]
}
