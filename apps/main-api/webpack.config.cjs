// apps/main-api/webpack.config.cjs
const { swcDefaultsFactory } = require("@nestjs/cli/lib/compiler/defaults/swc-defaults");
const nodeExternals = require("webpack-node-externals");
const path = require("node:path");

const swcDefaultConfig = swcDefaultsFactory().swcOptions;

module.exports = {
	externals: [
		nodeExternals({
			allowlist: [],
		}),
	],
	resolve: {
		extensions: ['.ts', '.js'],
		extensionAlias: {
			'.js': ['.ts', '.js'],
		},
		alias: {
			'@': path.resolve(__dirname, 'src'),
		},
	},
	module: {
		rules: [
			{
				test: /\.ts$/,
				exclude: /node_modules/,
				use: {
					loader: "swc-loader",
					options: swcDefaultConfig,
				},
			},
		],
	},
};
