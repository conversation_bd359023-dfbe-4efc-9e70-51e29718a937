{"name": "my-monorepo", "private": true, "packageManager": "bun@1.2.3", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "typecheck": "turbo run typecheck", "clean": "turbo run clean", "format": "turbo run format", "db:generate": "turbo run generate --filter=@monorepo/db", "db:migrate": "turbo run migrate --filter=@monorepo/db", "db:push": "turbo run push --filter=@monorepo/db", "db:studio": "turbo run studio --filter=@monorepo/db", "format:check": "biome check .", "format:fix": "biome check . --apply", "lint:biome": "biome lint .", "lint:biome:fix": "biome lint . --apply", "format:biome": "biome format .", "format:biome:fix": "biome format . --write"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/core": "^1.11.24", "swc-loader": "^0.2.6", "turbo": "^2.5.3"}, "dependencies": {"@types/node": "^20.17.51"}, "trustedDependencies": ["@biomejs/biome", "@nestjs/core", "@swc/core", "core-js", "esbuild", "nest<PERSON><PERSON>-pino", "sharp", "unrs-resolver"]}